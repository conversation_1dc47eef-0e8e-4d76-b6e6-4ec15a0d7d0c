{"name": "chaos-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint --ext .js,.ts,.vue src", "lint:fix": "eslint --ext .js,.ts,.vue src --fix", "format": "prettier --write \"src/**/*.{js,ts,vue,css}\"", "deploy": "npx -y rth-host-helper@latest --site junzaisun --build build --outdir dist"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.9.0", "element-plus": "^2.9.6", "gsap": "^3.12.7", "pinia": "^3.0.1", "swiper": "^11.2.6", "vue": "^3.5.13", "vue-i18n": "^9.2.2", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-vue": "^10.0.0", "less": "^4.2.2", "less-loader": "^12.2.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}