// 全局颜色变量
@primary-color: #2d374b;
@secondary-color: #1890ff;
@text-primary: rgba(255, 255, 255, 0.9);
@text-secondary: rgba(255, 255, 255, 0.7);
@section-bg: rgba(255, 255, 255, 0.05);

// 布局变量
@border-radius: 4px;
@container-width: 1200px;

// 断点变量
@screen-sm: 576px;
@screen-md: 768px; 
@screen-lg: 992px;
@screen-xl: 1200px;

// 混合
.flex-center() {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-shadow() {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
} 