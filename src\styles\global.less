//=============================== 全局字体 ===============================
@font-family-base: "Alibaba PuHuiTi 3.0";

/* 字体声明 */
@font-face {
  font-family: "Alibaba PuHuiTi 3.0";
  src: url("@/assets/fonts/AlibabaPuHuiTi-3-55-Regular/AlibabaPuHuiTi-3-55-Regular.woff2") format("woff2"),
       url("@/assets/fonts/AlibabaPuHuiTi-3-55-Regular/AlibabaPuHuiTi-3-55-Regular.woff") format("woff"),
       url("@/assets/fonts/AlibabaPuHuiTi-3-55-Regular/AlibabaPuHuiTi-3-55-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 加载中等粗体版本(如果有) */
@font-face {
  font-family: "Alibaba PuHuiTi 3.0";
  src: url("@/assets/fonts/AlibabaPuHuiTi-3-65-Medium/AlibabaPuHuiTi-3-65-Medium.woff2") format("woff2"),
       url("@/assets/fonts/AlibabaPuHuiTi-3-65-Medium/AlibabaPuHuiTi-3-65-Medium.woff") format("woff"),
       url("@/assets/fonts/AlibabaPuHuiTi-3-65-Medium/AlibabaPuHuiTi-3-65-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* 加载粗体版本 */
@font-face {
  font-family: "Alibaba PuHuiTi 3.0";
  src: url("@/assets/fonts/AlibabaPuHuiTi-3-85-Bold/AlibabaPuHuiTi-3-85-Bold.woff2") format("woff2"),
       url("@/assets/fonts/AlibabaPuHuiTi-3-85-Bold/AlibabaPuHuiTi-3-85-Bold.woff") format("woff"),
       url("@/assets/fonts/AlibabaPuHuiTi-3-85-Bold/AlibabaPuHuiTi-3-85-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

body {
  font-family: @font-family-base, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

//=================================全局提示框 ===============================
.coming-soon-message {
  background: linear-gradient(135deg, #0072FF 0%, #00C6FF 100%) !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 12px 20px !important;
  font-weight: 500 !important;
  box-shadow: 0 6px 16px rgba(0, 114, 255, 0.2) !important;
  min-width: 280px !important;
  backdrop-filter: blur(5px) !important;
}

.coming-soon-message .el-message__content {
  color: white !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
}

.coming-soon-message .el-message__closeBtn {
  color: rgba(255, 255, 255, 0.8) !important;
}

.coming-soon-message .el-message__closeBtn:hover {
  color: white !important;
}

.coming-soon-message .el-message__icon {
  color: white !important;
  margin-right: 10px !important;
  font-size: 18px !important;
}

// ==============================导航菜单相关================================
.el-menu--horizontal.el-menu--popup-container.industry-submenu {
  max-width: 100vw !important;
  background-color: transparent !important; /* 与主导航背景色一致 */

  /* 行业子菜单和产品中心子菜单的通用样式 */
  // ul
  .el-menu {
    width: 100vw;
    display: flex;  /* 改为flex布局以便更精确控制间距 */
    justify-content: center;  /* 居中显示所有菜单项 */
    padding: 0;
    height: 100px; /* 设置固定高度为70px */
    margin-top: -0.7rem; //间隙
    // font-weight: bolder; //单项子菜单粗细
  }

  // li
  .el-menu-item {
    height: 100px; /* 设置菜单项高度为70px */
    line-height: normal;
    display: flex;
    flex-direction: column;
    align-items: center; /* 确保水平居中 */
    justify-content: center; /* 确保垂直居中 */
    text-align: center;
    padding: 0.25rem 0.5rem;
    margin: 0 25px;  /* 两边25px间距，两项之间就是50px */
    border-radius: 0;
    position: relative; /* 添加相对定位 */
    text-shadow: none; /* 移除文字阴影 */

    &:hover {
      background-color: transparent !important;
    }

    img {
      width: 1.5rem; /* 增大图标尺寸 */
      height: 2.5rem;
      object-fit: contain;
      margin-bottom: 0.5rem;
      display: block; /* 确保图标是块级元素 */
      margin-left: auto; /* 左右边距自动，实现水平居中 */
      margin-right: auto;
      filter: brightness(1.3); /* 提高亮度 */
    }

    span {
      display: block;
      width: 100%; /* 确保span占满整个宽度 */
      text-align: center;
      white-space: nowrap; /* 防止文本换行 */
      font-size: 0.875rem;
      text-shadow: none; /* 移除文字阴影 */
    }
  }

// ==============================产品中心子菜单================================
  /* 产品中心子菜单特殊处理 */
  .el-menu-item-group {
    display: contents; /* 使组标题和项目能正确放置在网格中 */

    .el-menu-item-group__title {
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      color: #fff;
      font-weight: 500;
      border-bottom: 1px solid #eee;
      margin-bottom: 0.5rem;
      grid-column: 1 / -1; /* 标题占据整行 */
      text-align: center; /* 确保标题文本居中 */
    }
  }

  /* 针对产品中心的子菜单调整为4列 */
  &.products-submenu .el-menu {
    grid-template-columns: repeat(4, 1fr);
  }
}

//行业方案图片自适应
.el-menu--horizontal.el-menu--popup-container.industry-submenu.industry-submenu-for-img {
  img {
    width: 1.5rem; /* 图标尺寸 */
    height: 2.5rem;
    object-fit: contain;
  }
}

.industry-submenu {
  transform: translate(0, 0); /* 修复位置偏移问题 */
  left: 0 !important; /* 强制左对齐 */
  right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  box-shadow: none !important;
  /* 添加背景色设置，与主导航条背景一致 */
  background-color: transparent !important; /* 或与您网站的深色背景匹配 */


}

/* 调整第一个菜单项"钢铁"的位置，使其与"行业方案"对齐 */
.industry-submenu .el-menu-item{
  // margin-left: calc(50% - 450px); /* 根据行业方案的位置调整 */
  position: relative;
  left: -90px; /* 根据实际情况调整，使"钢铁"位于"行业方案"下方 */
 }

/* 确保下拉菜单的底部没有多余边距引起白条 */
.el-popper.is-light {
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 产品中心子菜单样式 */
.el-menu--horizontal.el-menu--popup-container.products-submenu {
  max-width: 100vw !important;
  background-color: transparent !important;
  margin: 0 !important;
  padding: 0 !important;

  .el-menu {
    width: 100vw;
    display: flex !important;
    justify-content: center;
    padding: 20px 0;
    min-height: 200px;
    margin: 0;
    box-shadow: none !important;
    margin-top: -0.7rem;
  }

  /* 分组容器样式 */
  .el-menu-item-group {
    display: flex;
    flex-direction: column;
    margin: 0 30px;
    min-width: 220px;

    /* 添加产品中心菜单项鼠标悬停效果 */
    .el-menu-item {
      &:hover {
        color: #409EFF !important; /* 鼠标悬停时文字变蓝色 */
        background-color: transparent !important;
      }
    }

    /* 分组标题样式 */
    .el-menu-item-group__title {
      padding: 0;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        display: inline-block;
        width: 32px; /* 从26px增加到32px */
        height: 32px; /* 从26px增加到32px */
        margin-right: 10px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        filter: brightness(1.3) contrast(1.1); /* 提高亮度和对比度 */
      }
    }

    /* 智慧软件图标 */
    &:first-child .el-menu-item-group__title::before {
      background-image: url('@/assets/header/hover-software.png');
      width: 1.3rem;
      height: 2.3rem;
    }

    /* 智能硬件图标 */
    &:last-child .el-menu-item-group__title::before {
      background-image: url('@/assets/header/hover-hardware.png');
      width: 1.5rem;
      height: 2.5rem;
    }
  }
}

/* 修复产品中心和行业方案下拉菜单的定位 */
.products-submenu, .industry-submenu {
  transform: translate(0, 0) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  border: none !important;
}

/* 去除下拉菜单的阴影 */
.el-menu--popup{
  box-shadow: none !important;
}

// 添加一级导航菜单加粗样式
.el-menu--horizontal > .el-menu-item,
.el-menu--horizontal > .el-sub-menu > .el-sub-menu__title {
  font-weight: 500 !important;
}

//1366px分辨率下，产品中心子菜单样式
@media (max-width: 1366px) {
  .el-menu--horizontal {
    height: auto !important;
  }
}
