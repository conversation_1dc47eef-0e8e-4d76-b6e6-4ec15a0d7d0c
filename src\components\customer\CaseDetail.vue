<template>
  <div class="case-detail" @wheel.stop="handleScroll" @touchmove.stop="handleScroll">
    <div class="header">
    </div>
    <div class="content">
      <div class="content-header">
        <div class="header-title">
          <span class="header-title-main">{{ showContent.mainTitle }}</span>
          <p>{{ showContent.subTitle }}</p>
        </div>
      </div>
      <div class="content-bottom">
      <div class="return-button" @click="handleReturn">
        <el-icon><ArrowLeft /></el-icon>
        <span>返回</span>
      </div>
        <div class="sub-title">项目背景</div>
        <p class="sub-content" v-html="showContent.background"></p>
        <div class="sub-title">建设内容</div>
        <div class="sub-content" v-html="showContent.content">  </div>
        <div class="sub-title">应用成果</div>
        <div class="sub-content" v-html="showContent.achievement" ></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'CaseDetail'
}
</script>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue';
import image3 from '@/assets/customer/image3.png';
import image4 from '@/assets/customer/image4.png';
import image5 from '@/assets/customer/image5.jpg';
import image6 from '@/assets/customer/image6.png';
import image8 from '@/assets/customer/image8.png';
import image9 from '@/assets/customer/image9.png';
import image10 from '@/assets/customer/image10.png';
import image11 from '@/assets/customer/image11.png';
import image12 from '@/assets/customer/image12.png';
import image13 from '@/assets/customer/image13.png';
import image14 from '@/assets/customer/image14.png';
import image15 from '@/assets/customer/image15.png';
import image16 from '@/assets/customer/image16.png';
import image17 from '@/assets/customer/image17.png';
import image18 from '@/assets/customer/image18.png';
import image19 from '@/assets/customer/image19.png';
import image20 from '@/assets/customer/image20.png';
import { useSectionStore } from '@/stores/sectionStore'

const router = useRouter()

// Function to handle the return to the case list
const handleReturn = () => {
  router.push('customer?section=1')
}

type CaseData = {
  mainTitle: string;
  subTitle: string;
  background: string;
  content: string;
  achievement: string;
};
const customer: Record<string, CaseData> = {
  baogangphm: {
    mainTitle: '中国宝武钢铁集团有限公司',
    subTitle: '设备预测性维护与健康管理系统',
    background: '作为国内钢铁行业龙头企业，企业风机设备长期依赖美国某工业监测系统，存在数据封闭、运维费用高昂、远程监控缺失等问题。原系统仅保护关键设备，且需支付高额费用接入远程平台，导致大部分风机缺乏实时监测与预警能力，设备运行效率与安全性难以保障。为打破国外技术垄断、降低运维成本并实现全面数字化管理，宝武钢铁联合凯奥思数据启动风机设备预测性维护与健康管理系统，旨在通过国产化替代方案，构建覆盖全厂风机的智能化监控体系。',
    content: `
      <p>项目以数据采集为核心，可视化为基础，综合展示风机系统的运行状态及报警状态。并将设备实时监控与设备运维进行融合，以监控指导运维，打造"端云协同"智能监控体系，全面突破技术壁垒。</p>
      <img src="${image3}"></img>
      <img src="${image4}"></img>
      <b>边缘端：</b>
      <b>自主研发Chaos Data Watch边缘智能硬件，实现千台风机数据秒级采集与精准解析，无缝兼容国际主流系统，破解"数据孤岛"难题，构建全厂设备互联底座。</b>
      <b>智慧平台：</b>
      <b>平台支撑：集成物联网、大数据及运维平台，支持设备全生命周期管理，涵盖实时监控、工艺分析、智能诊断等功能。</b>
      <b>知识库建设：融合设备基础库、轴承库、故障库及标准库，形成结构化数据库，实现调试、检修流程标准化与数据驱动决策。</b>
      <b>智能运维：新增自适应阈值预警、趋势预警及多变量分析功能，报警延迟低于5秒，支持微信推送与闭环处理；整合工单管理、巡点检、保养计划等模块，提升运维效率。</b>
      <b>工艺优化：支持多参数组态分析、历史趋势对比及工艺监测预警，助力能耗精细化管理。</b>
      `,
    achievement: `
      <b>国产化突破：成功替代国外系统，接入上千台风机，实现数据共享与低成本运维，打破技术垄断。</b>
      <b>效能提升：预警准确率达92%，设备健康评估与故障预测能力显著增强。</b>
      <b>管理升级：建成标准化知识库与运维体系，工单处理、报警闭环效率提升；线上/线下协同机制优化资源调配，降低维护成本。</b>
      <b>经济效益：通过能效监测与优化，设备故障率下降，延长设备寿命，年运维成本大幅下降。</b>
    `
  },
  jingyephm: {
    mainTitle: '敬业钢铁有限公司',
    subTitle: '设备预测性维护与健康管理系统',
    background: '随着钢铁行业对设备可靠性、生产效率的要求不断提升，传统"被动维修"模式已难以满足大规模、高复杂度设备的运维需求。敬业钢铁为应对设备非计划停机频发、维护成本高、故障诊断依赖人工经验等痛点，联合行业技术团队，启动设备预测性维护与健康管理系统建设项目，旨在通过智能化、数据化手段重构设备管理体系，实现从"事后维修"向"主动预防"的转型，为生产连续性和降本增效提供技术支撑。',
    content: `
      <img src="${image3}"></img>
      <b>全厂级数据融合与智能监控 :</b>
      <b>系统整合51个车间、900+台核心设备的7000+测点数据，覆盖振动、电压、电流、温度等多维参数，并通过标准化接口集成振动监测、电气监控等异构系统，构建低频运行数据与高频振动波形的统一管理平台。基于B/S架构，搭建"集团-基地-厂级-产线-区域-设备"六级驾驶舱，实时展示设备健康度评分、报警趋势、运维统计等核心指标，实现全厂设备状态的可视化管控。</b>
      <b>多模型预警与智能诊断: </b>
      <b>依托AI算法、机理模型与专家经验，建立阈值预警、趋势预警、机理预警、AI预警四重智能分析体系。例如，通过振动频谱分析精准识别轴承磨损、齿轮箱异常等潜在故障，结合故障案例库自动生成诊断报告（故障现象、原因分析、解决建议），故障处理时间缩短。平台还支持时域波形、频域分析、轴心轨迹等20余种专业图谱工具，为技术人员提供精准决策依据。</b>
      <b>知识沉淀与闭环管理 :</b>
      <b>构建设备故障案例库、机理模型库及轴承参数库，沉淀专家诊断经验与设备运行规律。通过可视化机理建模工具，实现故障规则的可配置化（如转子不平衡、轴承故障等），并支持诊断报告的自动生成与闭环管理，形成"监测-预警-诊断-优化"全流程数字化运维生态。</b>
      `,
    achievement: `
      <b>降本增效：预防性维护替代被动维修，设备综合利用率提升15%，年度维护成本降低20%，故障率下降30%。</b>
      <b>安全可靠：实时监控设备健康状态，减少突发故障风险，关键设备非计划停机时长降低，保障生产连续性。</b>
      <b>管理升级：建成标准化知识库与运维体系，工单处理、报警闭环效率提升；线上/线下协同机制优化资源调配，降低维护成本。</b>
      <b>技术赋能：沉淀超500例故障案例与100+机理模型，形成企业专属知识资产，助力技术团队快速定位复杂故障，推动运维经验标准化、可复制化。</b>
    `
  },
  hailuophm: {
    mainTitle: '安徽海螺水泥股份有限公司',
    subTitle: '设备预测性维护与健康管理系统',
    background: '海螺水泥宁国厂作为水泥行业标杆企业，其核心产线设备（如高温风机、窑主减速机、辊压机等）面临传统运维模式下的痛点：设备状态依赖人工巡检，故障预警滞后，维护成本高昂。尽管行业在单设备故障诊断技术上有所突破，但将产线级集中监控、健康度评估与智能预警深度融合的解决方案仍属空白。为打破这一瓶颈，海螺水泥联合南京凯奥思数据技术，于2022年启动海螺水泥宁国厂设备预测性维护与健康管理项目，旨在通过物联网、大数据与AI技术，构建覆盖72台关键设备的智能化运维体系，实现从"经验驱动"向"数据驱动"的运维模式转型。',
    content: `
      <b>项目以"端-边-云"协同架构为核心，打造全链路智能化运维平台</b>
      <img src="${image4}"></img>
      <b>智能感知层：部署振温一体传感器、应力波传感器及无线采集站，覆盖设备轴承、齿轮箱等关键测点，实时采集振动、温度等数据，兼容有线/无线传输，破解复杂工况下的数据采集难题。</b>
      <b>边缘计算层：采用自主研发的DW2700、DW1900智能采集终端，实现数据本地化处理与特征提取，支持自适应阈值预警、趋势分析及多变量诊断，告警响应延迟≤5秒。 </b>
      <img src="${image5}"></img>
      <b>云端智能平台：PHM预测性运维系统：集成设备实时监控、故障报警、工单管理模块，提供时域分析、频域分析等专家工具，支持微信、短信多级告警推送与闭环处理。</b>
      <b>健康度评估模型：结合振动频谱、工艺参数与机器学习算法，开发电机不平衡、减速机齿轮磨损等20余类设备诊断模型，构建产线级动态评分体系，实现设备健康状态量化评估。能效优化模块：通过多参数组态分析与历史趋势对比，优化回转窑等核心设备能效，降低单位能耗。</b>
      `,
    achievement: `
      <b>运维管理升级:系统通过设备状态监控和故障报警触发维护检修，有效降低突发性非计划停机事故发生的概率，以提高工业产线的生产效率和设备开动率，提升直接经济效益。系统建成标准化设备知识库与动态健康评估体系，提升管理水平。</b>
      <b>技术标杆效应:系统成功接入72台设备，覆盖破碎、烧成、水泥磨等全工序，形成可复制的"监测-预警-诊断-优化"闭环方案，为海螺集团多基地推广奠定基础，目前已在8家水泥厂成功应用。助理水泥厂快速建立生产线关键设备的专业化、智能化管理平台。</b>
    `  
  },
  jiaomeiphm: {
    mainTitle: '山西焦煤集团有限责任公司',
    subTitle: '设备预测性维护与健康管理系统',
    background: '随着煤矿智能化转型的深入推进，山西焦煤集团高阳煤矿亟需解决传统设备运维中存在的响应滞后、故障诊断效率低等问题。为提升矿井设备安全管理水平，项目深度融合人工智能、工业物联网、云计算及大数据技术，搭建覆盖副斜井提升、排水、压风、通风、运输五大系统和26台设备的智能监测与故障诊断平台，旨在实现设备状态的全面感知、远程诊断与动态预测，推动煤矿生产向"精准运维"模式升级。',
    content: `
      <b>围绕"数据驱动"与"智能诊断"两大核心，构建了多维设备健康管理体系：</b>
      <b>智能感知层：部署PLC、智能传感器等硬件设备，实时采集振动、温度、压力等关键参数，支持Modbus、OPC等多种工业协议数据接入。</b>
      <b>数据处理与分析层：通过高频振动信号处理技术（如频谱分析、包络解调、阶比跟踪），提取设备故障特征；结合专家知识库与智能诊断模型，实现轴承磨损、齿轮故障等隐患的精准识别。</b>
      <b>应用服务层：打造"管理驾驶舱"，集成三维流程展示、实时健康评估、预警统计、趋势分析等功能，支持多系统数据联动与远程专家交互诊断。</b>
      <b>运维闭环管理：建立设备台账、点检报告、诊断报告等数据库，提供自检优化、报警处理跟踪及自动生成日报/月报功能，形成全生命周期运维闭环。</b>
      <img src="${image6}"></img>
      `,
    achievement: `
      <b>故障预警效率提升:通过实时监测与智能分析，设备异常检出率提高，误报率降低40%，实现故障"早发现、早处理"。</b>
      <b>运维成本优化:自动生成运维报表减少人工统计工作量，诊断报告结合专家知识库使故障定位准确率提升，检修效率提高30%。</b>
      <b>决策支持增强:多维数据驾驶舱为管理者提供实时设备状态概览与趋势预测，辅助科学决策。</b>
      <b>安全效益显著:依托远程诊断与动态预警，设备异常停机风险降低70%，为煤矿安全生产提供技术保障。</b>
    `   
  },
  basifuphm: {
    mainTitle: '巴斯夫（中国）有限公司',
    subTitle: '设备预测性维护与健康管理系统',
    background: '随着化工行业对设备安全性和连续生产要求的提升，传统人工巡检与被动维修模式已难以满足高效运维需求。上海巴斯夫面临关键设备（如空冷风机、罗茨风机、搅拌机等）非计划停机频发、故障诊断依赖人工经验、维护成本高等挑战。为此，企业联合凯奥思数据启动设备在线监测与智慧运维系统建设项目，旨在通过物联网、大数据和AI技术实现设备智慧运维，推动从"事后维修"向"预测性维护"转型，保障生产连续性与降本增效。',
    content: `
      <img src="${image8}"></img>
      <b>全场景数据采集与边缘计算 </b>
      <b>在Infra和Basonat装置的电机、齿轮箱等关键部位部署DN-101无线振温传感器，实时采集振动、温度等多维度数据，覆盖54个测点。通过边缘智能网关实现数据预处理（降噪、特征提取），结合5G网络将数据上传至云端，构建"传感器-边缘层-云端"三级架构，确保数据实时性与分析效率。</b>
      <b>智能预警与诊断模型开发 </b>
      <b>基于设备特性开发电机类、齿轮箱类专业诊断模型（如转子不平衡、轴承故障、齿轮磨损等8类故障机理），融合阈值预警、趋势预警、AI预警等多模式分析。通过可视化建模工具配置故障规则，结合时域波形、频谱分析等20余种工具，精准定位异常并生成诊断报告（含故障原因、解决建议）。</b>
      <b>PHM软件系统功能落地 </b>
      <b>搭建云端智能运维平台，实现设备状态实时监控、健康度评分、报警趋势分析等功能。支持多层级驾驶舱（集团-基地-产线），提供设备档案管理、运维工单闭环、移动端远程查看等模块，并集成ISO-10816国际振动标准，动态调整报警阈值，减少误报率。 </b>
      `,
    achievement: `
      <b>设备实时监控</b>
      <b>实时监测设备运行状态，达到异常早知道、早预报、早诊断，把故障消灭在萌芽之中、减少设备停机时间，提高设备状态把控能力和设备管理水平，为检修计划的制定提供依据，降低维修成本。</b>
      <b>提高检修效率</b>
      <b>助力提升上海巴斯夫远程设备管理与智能化运维技术水平，减少现场设备点检员工作量，提高厂区设备的检修、维修等工作效率。</b>
      <b>故障智能预警</b>
      <b>预测性维护替代被动维修，系统根据单参数分析或多参数的联合分析，探索设备的故障特征机理诊断模型及趋势预警模型，对设备运行状态数据进行远程看护，并及时通知现场管理人员设备异常状态。</b>
      <b>运维知识沉淀</b>
      <b>沉淀超50例故障案例与8类机理模型，形成标准化运维知识库，推动经验数字化。</b>
    `  
  },
  fengtianphm: {
    mainTitle: '广汽丰田汽车有限公司',
    subTitle: '设备预测性维护与健康管理系统',
    background: '广汽丰田作为汽车制造领域的标杆企业，面临设备种类多、产线复杂、传统维护模式效率低等挑战。设备突发故障导致的非计划停机、维护成本高企等问题，亟需通过智能化手段实现设备状态实时监测与预测性维护。凯奥思数据基于工业物联网与预测性健康管理技术，为广汽丰田厂区生产线提供设备预测性维护与健康管理系统，覆盖成型、涂装、检查、废料处理等核心产线，推动其从被动维修向主动预测性维护转型。',
    content: `
      <img src="${image9}"></img>
      <img src="${image10}"></img>
      <b>设备智能化改造 </b>
      <b>在循环泵、风机、冷冻机、干燥炉等关键设备上加装振动、温度、油液品质、微压差等传感器，实现设备运行数据的实时采集与传输</b>
      <b>PHM系统部署 </b>
      <b>搭建在线监测平台，集成数据可视化、趋势分析、阈值报警等功能，结合ISO 10816标准优化报警阈值算法，显著降低误报率。</b>
      <b>远程诊断与看护 </b>
      <b>通过专家系统分析设备振动频谱、温度变化等数据，生成诊断报告并远程指导现场维护。例如，针对燃烧循环风机振动异常，精准识别转子动平衡问题；通过红外摄像头实现干燥炉高温区域动态监控，预防过热风险。</b>
      <b>数据治理与运维保障</b>
      <b>开展多轮数据准确性校验，确保传感器数据误差控制在合理范围（振动误差≤25%，温度误差≤8℃），并建立快速响应机制处理传感器故障与数据中断问题。</b>
      `,
    achievement: `
      <b>故障预警能力增强：报警准确性提升70%，报警数量从1530条/月降至500条/月，关键设备故障识别率达95%以上，平均故障处理周期缩短40%。</b>
      <b>维护成本优化：通过预测性维护减少非计划停机时间30%，典型案例中，搪塑机风机因薄膜破损导致的振动异常被提前预警，修复后振幅下降60%，避免连带损失。</b>
      <b>智能化转型标杆：形成覆盖9大类设备、超2000个测点的监测网络，每月输出设备健康分析报告，为科学制定检修计划提供数据支撑，推动丰田工厂向数字化、智能化运维迈进。</b>
      <b>预测性维护替代被动维修，系统根据单参数分析或多参数的联合分析，探索设备的故障特征机理诊断模型及趋势预警模型，对设备运行状态数据进行远程看护，并及时通知现场管理人员设备异常状态。</b>
    `     
  },
  jiaomeieam: {
    mainTitle: '山西焦煤集团有限责任公司',
    subTitle: '设备全生命周期管理系统',
    background: '在国家"新型工业化"与"数字中国"战略驱动下，煤炭行业智能化转型成为提升产业竞争力的关键路径。山西焦煤集团积极响应国家《煤矿智能化建设指南》及山西省"两个转型"部署，以数字化技术重构设备管理模式，推动传统能源行业向智能运维与精益管理升级。项目聚焦设备全生命周期管理，通过融合工业互联网、大数据及人工智能技术，构建覆盖集团与矿井的智能管理平台，助力煤矿生产向安全、高效、可持续方向转型。',
    content: `
      <b>围绕"数据贯通、智能决策、生态协同"三大目标，打造一体化设备管理平台 </b>
      <b>全域数据融合：建立科学设备编码体系，统一主数据标准，打通集团与矿井数据链路，集成振动、温度等多维参数实时采集，覆盖通风、排水、运输等核心系统。</b>
      <b>智能诊断体系：基于振动分析、工况建模与专家知识库，构建动态监控诊断中心，实现设备故障早期预警与精准定位，推动检修模式从"计划性"向"预测性"转变。 </b>
      <b>全流程闭环管理：搭建设备管理中心，覆盖台账管理、需求计划、维保工单等功能，支持无纸化办公与语音交互查询，提升管理效率；通过决策看板整合设备健康评分、趋势预测与资源优化建议，赋能科学决策。</b>
      <img src="${image11}"></img>
      <img src="${image12}"></img>
      `,
    achievement: `
      <b>运维模式升级</b>
      <b>平台通过实时监测设备振动频谱与运行参数，提前识别压风系统螺杆磨损、皮带机轴承故障等隐患，实现"故障预判-精准检修-闭环验证"的全流程管理，大幅减少非计划停机，保障生产连续性。</b>
      <b>管理效率提升</b>
      <b>电子化台账与智能搜索功能简化设备档案调取流程，语音交互与分词检索提升操作便捷性；知识库沉淀专家经验，推动维修流程标准化，缓解高技能人才短缺压力，加速新人培养。安全与效益双赢</b>
      <b>安全与效益双赢</b>
      <b>远程监控与少人化运维降低井下作业风险，动态健康评分系统优化维护周期，延长设备寿命；数据驱动的决策看板助力资源精准配置，减少"过剩检修"与"检修不足"的盲区，显著降低运维成本。</b>
      <b>行业价值引领</b>
      <b>项目以"数据+智能"重塑煤矿设备管理模式，为传统能源行业数字化转型提供可复制经验，推动行业从"经验依赖"向"技术驱动"跨越。</b>
      `     
  },
  tianchieam: {
    mainTitle: '新疆天池能源有限责任公司',
    subTitle: '设备全生命周期管理系统',
    background: '新疆天池能源有限责任公司是特变电工立足新疆优势资源转换战略投资设立的大型能源企业，拥有多座选煤厂及数十条生产线，规划年产能超亿吨。随着生产规模扩大，现有REP系统、PM工程管理系统及综合预警平台等独立模块数据割裂，设备管理智能化水平滞后，难以支撑矿区高效运营需求。为破解系统孤岛、提升设备管理效能，公司启动智能化设备全生命周期管理系统建设，旨在整合现有系统资源，打通数据壁垒，构建覆盖设备"前期规划-中期运维-后期优化"的全流程管理体系，推动矿区向"少人化、智能化、精益化"转型。',
    content: `
      <b>全流程覆盖：围绕设备全生命周期，开发基础管理、智能点巡检、智能润滑、故障诊断等九大模块，贯通设备档案、运维、检修及报废流程，实现管理科学化、业务标准化。</b>
      <b>系统深度整合：横向联动OA、WMS、能耗管理等系统，纵向集成点检、在线监测及故障诊断工具，打破数据孤岛，构建设备动静态台账、备件消耗分析、成本预算追踪等闭环管理链条。</b>
      <b>智能决策支撑：依托设备故障知识库与实时监测数据，建立预警预测模型，优化维护计划与资源配置，降低停机率与维修成本；通过智慧仓储与语音交互功能，提升操作效率，助力"无人化"目标落地。 </b>
      <b>智慧矿山赋能：以平台为核心，推动设备管理业务流程扁平化、信息集成化，为矿区安全生产、降本增效及战略决策提供数据引擎。</b>
      <img src="${image13}"></img>
      <img src="${image14}"></img>
      `,
    achievement: `
      <b>提高设备投资回报率</b>
      <b>根据设备状态监测、设备运行监视（运行小时数、运行公里数）等因素，生成科学的点检/保养建议，减少过度维护或维护不及时问题，由"故障被迫性维修"转变为"预防性维修"，降低设备故障率，延长设备寿命，提升投资回报率。</b>
      <b>提高工单质量和执行效率</b>
      <b>从设备采购进厂到设备最终报废，对设备进行全生命周期管理，通过即时维修、科学保养、合理路线、标准执行提高工单质量和执行效率。</b>
      `       
  },
  chaohuapc: {
    mainTitle: '巢湖海螺水泥有限责任公司',
    subTitle: '先进过程控制系统',
    background: `
      <b>近年来，工业企业行情低迷、产能过剩局面依旧严峻。供需失衡的格局下，故步自封已无法支撑企业立足当前复杂的市场形势，节能减碳、降本增效、产业转型升级、优化资产配置、增强核心竞争力才能维持水泥行业健康长远发展。</b>
      <b>国家发展改革委等部门发布《工业重点领域能效标杆水平和基准水平（2023年版）》，水泥熟料等领域的节能降耗发展方向和目标再一次被明确。水泥行业已经进入深度挖掘节能潜力阶段，企业也加快了推进技术改造项目的步伐，并且从拓展光伏、储能等技术应用，推动绿电、替代燃料替代传统能源转型等方面入手，不断寻找提升综合实力与市场竞争力的关键突破点。</b>
      <b>巢湖海螺顺应发展趋势，为减少人工经验依赖，根据能耗、产量、质量数据进行生产精准控制，同时降低经验不足和人工疲劳导致的经济损失与安全损失，提升整体综合实力，研发和应用生产控制全局优化软件对水泥产线进行技术改造项目。</b>
    `,
    content:  `
      <b>系统建设按照"科学规划+产研融合+优化迭代"的思路分步实施，聚焦平台和算法研发，并在水泥产线进行实施验证。</b>
      <b>1.平台集成数据采集、组件管理、控制编排、驾驶舱等核心功能，通过可视化、拖拉拽的方式实现生产控制流程配置，自定义生产控制策略，大大减少了源代码开发人员需求量，降低了系统使用门槛。</b>
      <b>2.算法采用参考借鉴+自主化的模式进行研发，实现 PID、MPC、LMI 等经典控制算法的自主化研发，并在此基础上融合机器学习、深度学习等优化类算法，控制效果显著，实现自主可控。</b>
      <b>3.产线应用针对水泥生产过程中的痛点和难点问题，利用自主研发的平台和算法与海螺工艺知识进行融合应用，对水泥生产过程中的"两磨一烧"进行控制优化，实现生产线的稳定、高效、优质运行，进一步降低能源消耗，提高劳动生产效率。</b>
      <img src="${image15}"></img>
      <img src="${image16}"></img>
      `,
    achievement:  `
      <b>通过多变量模型预测技术，实现煤磨系统、原料磨系统、烧成系统自动控制，降低劳动强度，关键参数控制效果提升明显，经实际考核，仅年煤耗降低可达189万元。实现自动控制回路在线率>98%，煤电耗指标降低＞1%，人员劳动强度下降＞90%，关键参数稳定性提升＞35%，游离钙标准偏差下降＞10%，年二氧化碳排放量下降＞1%。</b>
      <b>节约成本：减少人员劳动强度；减少在生产过程中的能源消耗；</b>
      <b>低碳环保：提升能源利用效率；减少污染的排放；</b>
      <b>品质提升：稳定控制参数波动，实现稳定加热和冷却；搅拌过程控制，确保不结块、不分层，保证成品的质量；</b>
      <b>稳定生产：减少温差波动，充分燃烧；及时响应各个变量之间的关联性。</b>
      `,
  },
  panjingapc: {
    mainTitle: '安徽盘景水泥有限公司',
    subTitle: '先进过程控制系统',
    background: '盘固集团盘景水泥厂为提高生产水泥质量，优化成本控制、生产品质、人员操作、安全隐患等因素的不确定性问题，建设专家控制系统，实现节约成本、低碳环保、品质提升、稳定生产等目标。',
    content:  `
      <b>均衡生产：通过对回转窑转速进行适当调整以维持整个窑系统的均衡稳定生产；</b>
      <b>稳定温度：控制温度波动幅度＜±5℃；</b>
      <b>稳定质量：游离钙标准偏差小于10%；</b>
      <b>降低煤耗： 主要控制分解炉出口温度或五级筒溜管温度以及分解炉出口CO含量,稳定喂煤；</b>
      <b>稳定风量：主要控制窑头罩负压，调节排风机挡板，保证入窑风量稳定。</b>
      <img src="${image17}"></img>
      `,
    achievement:  `
      <b>系统上线稳定运行后，在保证基本条件一致的情况下，分别用盘景水泥现有系统以及本系统开展24h、3*24h、7*24h对比，本系统分解炉炉温控制相较于现有系统有明显提升。软件平台连续运转率>95%，人员劳动强度明显降低；分解炉温度控制对比现有系统明显稳定，系统抗干扰能力明显优于现有系统，实现煤粉换仓等特殊工况解决措施。</b>
      `,
  },
  jingyeapc: {
    mainTitle: '敬业钢铁有限公司',
    subTitle: '先进过程控制系统',
    background: '在钢铁行业"双碳"目标及绿色转型的驱动下，白灰生产作为炼钢关键辅料环节，面临能耗高、工艺波动大、质量稳定性不足等挑战。传统回转窑生产依赖人工经验调节煤气流量、窑头负压等参数，易受操作水平差异影响，导致能源浪费与排放压力加剧。为破解这一难题，敬业钢铁引入先进过程控制系统，融合人工智能与专家经验，旨在实现生产流程标准化、能耗精细化管控，推动白灰窑向高效低碳方向升级。',
    content:  `
      <b>案基于白灰生产工艺的二级专家控制系统，根据白灰生产专家的专门知识和经验，借助人工智能技术和计算机控制技术，模拟专家对回转窑白灰生产过程中各操作变量进行实时智能过程控制。从而标准化白灰窑操作流程，实现生产过程中能耗的优化控制，达到节能减排、稳产稳质的控制要求。</b>
      <img src="${image18}"></img>
      <img src="${image19}"></img>
      `,
    achievement:  `
      <b>稳定生产过程</b>
      <b>系统投用率90%以上，与目标值偏差控制：煤气流量＜1%；窑头负压＜20%、窑尾温度＜1%</b>
      <b>吨产品节省燃煤消耗1.7kg标准煤</b>
      <b>提高经济效益</b>
      <b>单条白灰窑年创造综合经济效益80万元</b>
      `,
  },
  baogangapc: {
    mainTitle: '宝山钢铁股份有限公司',
    subTitle: '先进过程控制系统',
    background: '宝钢热轧1880产线加热炉使用基于PID的L1级基础自动化智能控制系统，并取得了不错的经济效益，不过在控制优化、节能降耗方面仍存在可以改善的空间。针对PID控制系统在时间延时、参数整定、多变量之间相互影响、多目标控制等问题上，提出基于先进过程控制的加热炉温度自动控制方案，以进一步改善控制系统效果，提高热轧生产效益。',
    content:  `
      <b>系统主要包含数据中心、组件管理、控制编排、驾驶舱、通用模块等模块。系统集成了高效率控制算法以及完备的软件安全体系架构，提供强大的建模、优化、控制基础能力，实现数据流、生产流与控制流的协同，提高生产效率，降低生产成本，实现自主可控的智能制造。</b>
      <img src="${image20}"></img>
      <b>1.解决时延问题。燃气阀门的开合度对炉温的影响存在时延，且时间相对较长，PID控制对于此类带时延控制存在不足，这是算法本身适用性不同造成的，会对温度控制效果产生一定的影响。</b>
      <b>2.解决多扰动问题。随着加热炉工况情况发生变化和各类扰动的出现而变化，这也给PID参数整定带来不便。</b>
      <b>3.解决强耦合问题。不同加热区之间温度会相互影响，前一加热段区间温度如果控制不好，直接影响下一区间的温度，PID控制算法本身并没有考虑到这一点，即将前一加热段区间温度作为变量加入后一加热段控制系统中也会影响温度控制的精度，存在改善的空间。</b>
      `,
    achievement:  `
      <b>系统实现在线率≥90%，主要被控参数稳定性提升20%-30%，1880产线均热段温度偏差(炉温设定值和实际炉温)可以控制在±20℃以下，其他加热段均有明显改善；1880产线加热炉燃耗下降≥1%~1.5%。</b>
      `,  
  },
}
const props = defineProps({
  caseId: {
    type: [Number, String],
    required: true,
  }
});

const showContent = ref({
  mainTitle: '暂无',
  subTitle: '暂无',
  background: '暂无',
  content: '暂无',
  achievement: '暂无',

})
watch(
  () => props.caseId,
  (newVal, oldVal) => {
    if (customer[newVal]) {
      showContent.value = customer[newVal]
    }
  },
  { deep: true, immediate: true }
)

const sectionStore = useSectionStore()

// Add scroll event handler
const handleScroll = (event: Event) => {
  event.stopPropagation()
}

// Add lifecycle hooks
onMounted(() => {
  document.body.classList.add('no-section-scroll')
  nextTick(() => {
    sectionStore.lockSection(true)
  })
})

onBeforeUnmount(() => {
  document.body.classList.remove('no-section-scroll')
  sectionStore.lockSection(false)
})
</script>

<style scoped lang="less">
// 根字体大小设置，用于rem计算
html {
  font-size: 16px;

  @media (min-width: 1920px) {
    font-size: calc(16px * (1920 / 1920));
  }

  @media (max-width: 1440px) {
    font-size: calc(16px * (1440 / 1920));
  }

  @media (max-width: 1280px) {
    font-size: calc(16px * (1280 / 1920));
  }
}

.case-detail {
  position: relative;
  width: 100%;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  /* 改为flex-start而非center */
  min-height: 100vh;

  /* 确保最小高度填满视口 */
  .header {
    box-sizing: border-box;
    width: 100%;
    height: 320px;
    display: flex;
    background: url('@/assets/customer/Group455.jpg') no-repeat center center;
    background-size: 100% 100%;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 120px;
    font-size: 24px;
    font-weight: 700;
    position: relative;
    
    .return-button {
      position: absolute;
      top: 20px;
      right: 20px;
      display: flex;
      align-items: center;
      gap: 5px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 8px 15px;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      font-weight: normal;
      transition: all 0.3s ease;
      z-index: 10;
      
      &:hover {
        background-color: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .el-icon {
        font-size: 16px;
      }
    }
  }

  .content {
    position: absolute;
    top: 49%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 100vh;
    display: flex;
    flex-direction: column;

    .content-header {
      box-sizing: border-box;
      padding-top: 80px;
      flex: 1;
      position: relative;

      .header-title {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding-top: 15px;
        padding-bottom: 35px;
        color: #fff;

        &-main{
          font-size: 24px;
          margin-bottom: 10px;
        }

        p {
          font-size: 32px;
        }
      }
    }

    .content-bottom {
      box-sizing: border-box;
      width: 100%;
      height: 80vh;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: auto;
      padding: 45px;
      padding-top: 40px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      position: relative;
      
      .return-button {
        position: absolute;
        top: 10px;
        left: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        cursor: pointer;
        background-color: white;
        color: #2985f7;
        // border: 1px solid #2985f7;
        border-radius: 4px;
        font-size: 14px;
        z-index: 100;
        
        .el-icon {
          margin-right: 2px;
          font-size: 14px;
        }
        
        &:hover {
          color: white;
          background-color: #2985f7;
        }
      }
      
      .sub-title {
        color: #2760ED;
        margin-top: 25px;
        font-size: 14px;
      }

      .sub-content {
        text-indent: 2em;
        margin: 15px 0;
        font-size: 14px;
        line-height: 1.8;
        white-space: pre-line;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: left;
        :deep(img){
          width: 40rem;
          height: auto;
          margin: 20px;
        }
        :deep(p){
        
          text-align: left;
          text-indent: 2em;
        }
        :deep(b){
          width: 100%;
          // font-weight: 700;
        }
        :deep(span){
          display: block;
          width: 100%;
        }
      }
    }
  }

}
</style>