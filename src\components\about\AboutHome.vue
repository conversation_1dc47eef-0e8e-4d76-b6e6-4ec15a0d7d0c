<template>
  <div class="home-hero">
    <div class="video-container">
      <video ref="videoElement" autoplay loop muted playsinline class="background-video">
        <source :src="videoSource" type="video/mp4">
        您的浏览器不支持 HTML5 视频。
      </video>
      <div class="overlay"></div>
    </div>
    <div class="content">
      <h1 class="title">凯奥思数据 让工业更智能</h1>
      <!-- <p class="subtitle">专注工业智能，助力企业数字化转型</p> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
// 直接导入视频文件
import videoSource from '@/assets/video/web2.mp4'

const videoElement = ref<HTMLVideoElement | null>(null)

onMounted(() => {
  if (videoElement.value) {
    videoElement.value.play().catch(error => {
      console.error('视频播放失败:', error)
    })
  }
})
</script>

<style scoped>
.home-hero {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色遮罩 */
  z-index: 2;
}

.content {
  position: relative;
  z-index: 3;
  text-align: center;
  color: #fff;
  padding: 0 20px;
  max-width: 800px;
}

.title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 24px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-button {
  padding: 12px 30px;
  font-size: 18px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .title {
    font-size: 36px;
  }
  
  .subtitle {
    font-size: 18px;
  }
}
</style> 